-- Create "agent_levels" table
CREATE TABLE "public"."agent_levels" (
  "id" bigserial NOT NULL,
  "name" character varying(50) NOT NULL,
  "meme_volume_threshold" numeric(38,2) NOT NULL,
  "contract_volume_threshold" numeric(38,2) NOT NULL,
  "meme_fee_rate" numeric(10,6) NOT NULL,
  "taker_fee_rate" numeric(10,6) NOT NULL,
  "maker_fee_rate" numeric(10,6) NOT NULL,
  "direct_commission_rate" numeric(10,6) NOT NULL,
  "indirect_commission_rate" numeric(10,6) NOT NULL,
  "extended_commission_rate" numeric(10,6) NOT NULL,
  PRIMARY KEY ("id")
);
-- Create "daily_user_volumes" table
CREATE TABLE "public"."daily_user_volumes" (
  "id" uuid NOT NULL DEFAULT gen_random_uuid(),
  "user_id" uuid NOT NULL,
  "date" date NOT NULL,
  "meme_volume_usd" numeric(38,2) NULL DEFAULT 0,
  "contract_volume_usd" numeric(38,2) NULL DEFAULT 0,
  PRIMARY KEY ("id")
);
-- Create index "idx_user_date" to table: "daily_user_volumes"
CREATE UNIQUE INDEX "idx_user_date" ON "public"."daily_user_volumes" ("user_id", "date");
-- Create "platform_volumes" table
CREATE TABLE "public"."platform_volumes" (
  "id" uuid NOT NULL DEFAULT gen_random_uuid(),
  "date" date NOT NULL,
  "total_meme_volume" numeric(38,2) NULL DEFAULT 0,
  "total_contract_volume" numeric(38,2) NULL DEFAULT 0,
  "fourteen_day_volume" numeric(38,2) NULL DEFAULT 0,
  "created_at" timestamptz NULL DEFAULT CURRENT_TIMESTAMP,
  "updated_at" timestamptz NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY ("id")
);
-- Create index "idx_platform_volumes_date" to table: "platform_volumes"
CREATE UNIQUE INDEX "idx_platform_volumes_date" ON "public"."platform_volumes" ("date");
-- Modify "users" table
ALTER TABLE "public"."users" ADD COLUMN "agent_level_id" bigint NULL DEFAULT 1, ADD COLUMN "level_grace_period_started_at" timestamptz NULL, ADD COLUMN "level_upgraded_at" timestamptz NULL, ADD COLUMN "first_transaction_at" timestamptz NULL, ADD CONSTRAINT "fk_users_agent_level" FOREIGN KEY ("agent_level_id") REFERENCES "public"."agent_levels" ("id") ON UPDATE NO ACTION ON DELETE NO ACTION;
-- Create index "idx_users_first_transaction_at" to table: "users"
CREATE INDEX "idx_users_first_transaction_at" ON "public"."users" ("first_transaction_at");
-- Create "commission_ledger" table
CREATE TABLE "public"."commission_ledger" (
  "id" uuid NOT NULL DEFAULT gen_random_uuid(),
  "recipient_user_id" uuid NOT NULL,
  "source_user_id" uuid NOT NULL,
  "source_transaction_id" character varying(100) NOT NULL,
  "source_transaction_type" character varying(20) NOT NULL,
  "commission_amount" numeric(36,18) NOT NULL,
  "commission_asset" character varying(10) NOT NULL,
  "status" character varying(20) NOT NULL DEFAULT 'PENDING_CLAIM',
  "created_at" timestamptz NULL DEFAULT CURRENT_TIMESTAMP,
  "updated_at" timestamptz NULL,
  "claimed_at" timestamptz NULL,
  PRIMARY KEY ("id"),
  CONSTRAINT "fk_commission_ledger_recipient_user" FOREIGN KEY ("recipient_user_id") REFERENCES "public"."users" ("id") ON UPDATE NO ACTION ON DELETE NO ACTION,
  CONSTRAINT "fk_commission_ledger_source_user" FOREIGN KEY ("source_user_id") REFERENCES "public"."users" ("id") ON UPDATE NO ACTION ON DELETE NO ACTION
);
-- Create index "idx_commission_ledger_recipient_user_id" to table: "commission_ledger"
CREATE INDEX "idx_commission_ledger_recipient_user_id" ON "public"."commission_ledger" ("recipient_user_id");
-- Create index "idx_commission_ledger_source_user_id" to table: "commission_ledger"
CREATE INDEX "idx_commission_ledger_source_user_id" ON "public"."commission_ledger" ("source_user_id");
-- Create index "idx_commission_ledger_status" to table: "commission_ledger"
CREATE INDEX "idx_commission_ledger_status" ON "public"."commission_ledger" ("status");
-- Modify "referral_snapshots" table
ALTER TABLE "public"."referral_snapshots" ADD COLUMN "l1_upline_id" uuid NULL, ADD COLUMN "l2_upline_id" uuid NULL, ADD COLUMN "l3_upline_id" uuid NULL, ADD CONSTRAINT "fk_referral_snapshots_l1_upline" FOREIGN KEY ("l1_upline_id") REFERENCES "public"."users" ("id") ON UPDATE NO ACTION ON DELETE NO ACTION, ADD CONSTRAINT "fk_referral_snapshots_l2_upline" FOREIGN KEY ("l2_upline_id") REFERENCES "public"."users" ("id") ON UPDATE NO ACTION ON DELETE NO ACTION, ADD CONSTRAINT "fk_referral_snapshots_l3_upline" FOREIGN KEY ("l3_upline_id") REFERENCES "public"."users" ("id") ON UPDATE NO ACTION ON DELETE NO ACTION;
-- Create index "idx_referral_snapshots_l1_upline_id" to table: "referral_snapshots"
CREATE INDEX "idx_referral_snapshots_l1_upline_id" ON "public"."referral_snapshots" ("l1_upline_id");
-- Create index "idx_referral_snapshots_l2_upline_id" to table: "referral_snapshots"
CREATE INDEX "idx_referral_snapshots_l2_upline_id" ON "public"."referral_snapshots" ("l2_upline_id");
-- Create index "idx_referral_snapshots_l3_upline_id" to table: "referral_snapshots"
CREATE INDEX "idx_referral_snapshots_l3_upline_id" ON "public"."referral_snapshots" ("l3_upline_id");
