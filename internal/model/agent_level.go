package model

import "github.com/shopspring/decimal"

type AgentLevel struct {
	ID                      uint            `gorm:"primaryKey" json:"id"`                                         // e.g., 1, 2, 3, 4, 5, 6
	Name                    string          `gorm:"type:var<PERSON>r(50);not null" json:"name"`                        // "Lv1", "Lv2", "Lv∞"
	MemeVolumeThreshold     decimal.Decimal `gorm:"type:numeric(38,2);not null" json:"meme_volume_threshold"`     // 30-day rolling volume
	ContractVolumeThreshold decimal.Decimal `gorm:"type:numeric(38,2);not null" json:"contract_volume_threshold"` // 30-day rolling volume

	// MEME Fee Rules (based on User's OWN level)
	MemeFeeRate decimal.Decimal `gorm:"type:numeric(10,6);not null" json:"meme_fee_rate"` // The effective fee for this level (e.g., 0.0080 for 0.80%)

	// Contract Fee Rules (based on platform-wide 14-day volume)
	TakerFeeRate decimal.Decimal `gorm:"type:numeric(10,6);not null" json:"taker_fee_rate"` // Taker fee rate (e.g., 0.0055 for 0.55%)
	MakerFeeRate decimal.Decimal `gorm:"type:numeric(10,6);not null" json:"maker_fee_rate"` // Maker fee rate (e.g., 0.0025 for 0.025%)

	// Commission Rules (rates for different depths)
	DirectCommissionRate   decimal.Decimal `gorm:"type:numeric(10,6);not null" json:"direct_commission_rate"`   // e.g., 0.30 for 30%
	IndirectCommissionRate decimal.Decimal `gorm:"type:numeric(10,6);not null" json:"indirect_commission_rate"` // e.g., 0.05 for 5%
	ExtendedCommissionRate decimal.Decimal `gorm:"type:numeric(10,6);not null" json:"extended_commission_rate"` // e.g., 0.025 for 2.5%
}
