package service

import (
	"context"

	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/model"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/repo"
)

type HyperLiquidTransactionServiceInterface interface {
	BulkInsertHyperLiquidTransactionsEvent(ctx context.Context, txEvents []model.HyperLiquidTransaction) error
}

type HyperLiquidTransactionService struct {
	transactionRepo repo.HyperLiquidTransactionRepositoryInterface
}

func NewHyperLiquidTransactionService() HyperLiquidTransactionServiceInterface {
	return &HyperLiquidTransactionService{
		transactionRepo: repo.NewHyperLiquidTransactionRepository(),
	}
}

func (s *HyperLiquidTransactionService) BulkInsertHyperLiquidTransactionsEvent(ctx context.Context, txEvents []model.HyperLiquidTransaction) error {
	return s.transactionRepo.BulkCreate(ctx, txEvents)
}
