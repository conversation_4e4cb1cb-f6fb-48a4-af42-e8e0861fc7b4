package repo

import (
	"context"

	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/global"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/model"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

// HyperLiquidTransactionRepository defines the interface for interacting with HyperLiquid transactions
type HyperLiquidTransactionRepositoryInterface interface {
	Create(ctx context.Context, tx *model.HyperLiquidTransaction) error
	BulkCreate(ctx context.Context, txs []model.HyperLiquidTransaction) error
}

type HyperLiquidTransactionRepository struct {
	db *gorm.DB
}

func NewHyperLiquidTransactionRepository() HyperLiquidTransactionRepositoryInterface {
	return &HyperLiquidTransactionRepository{
		db: global.GVA_DB,
	}
}

func (r *HyperLiquidTransactionRepository) Create(ctx context.Context, tx *model.HyperLiquidTransaction) error {
	transaction := r.db.WithContext(ctx).Model(&model.HyperLiquidTransaction{}).Create(tx)

	if transaction.Error != nil {
		return transaction.Error
	}

	return nil
}

func (r *HyperLiquidTransactionRepository) BulkCreate(ctx context.Context, txs []model.HyperLiquidTransaction) error {
	if len(txs) == 0 {
		return nil
	}

	// Use the database instance with context for bulk creation
	if err := r.db.WithContext(ctx).Model(&model.HyperLiquidTransaction{}).
		Clauses(clause.OnConflict{DoNothing: true}).
		CreateInBatches(txs, 100).Error; err != nil {
		return err
	}

	return nil
}
