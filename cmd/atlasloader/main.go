package main

import (
	"fmt"
	"io"
	"os"

	"ariga.io/atlas-provider-gorm/gormschema"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/model"
)

func main() {
	// define models to auto generate
	stmts, err := gormschema.New("postgres").Load(
		&model.User{},
		&model.UserWallet{},
		&model.Referral{},
		&model.ReferralSnapshot{},
		&model.AffiliateTransaction{},
		&model.SolPriceSnapshot{},
		&model.HyperLiquidTransaction{},
		&model.AgentLevel{},
		&model.CommissionLedger{},
		&model.DailyUserVolume{},
		&model.PlatformVolume{},
	)
	if err != nil {
		fmt.Fprintf(os.Stderr, "failed to load gorm schema: %v\n", err)
		os.Exit(1)
	}
	io.WriteString(os.Stdout, stmts)
}
